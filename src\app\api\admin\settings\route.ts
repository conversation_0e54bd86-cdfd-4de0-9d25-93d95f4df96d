import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check if user is authenticated and is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 403 }
      )
    }

    // Get current settings
    const { data: settings, error } = await supabase
      .from('site_settings')
      .select('*')
      .maybeSingle()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching settings:', error)
      return NextResponse.json(
        { error: 'Error fetching settings' },
        { status: 500 }
      )
    }

    // Return settings or default values if no settings exist yet
    const defaultSettings = {
      store_name: 'PrimeCaffe',
      store_description: '',
      contact_email: '<EMAIL>',
      contact_phone: '+41 44 123 45 67',
      default_language: 'de',
      default_currency: 'CHF',
      timezone: 'Europe/Zurich',
      points_per_chf: 1.0
    }

    return NextResponse.json(settings || defaultSettings)
  } catch (error) {
    console.error('Error in GET /api/admin/settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const updates = await request.json()
    console.log('POST /api/admin/settings - Updates received:', updates)

    const supabase = await createClient()
    
    // Check if user is authenticated and is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 403 }
      )
    }

    // Define allowed fields to prevent unauthorized updates
    const allowedFields = [
      'store_name',
      'store_description',
      'contact_email',
      'contact_phone',
      'default_language',
      'default_currency',
      'timezone',
      'points_per_chf'
    ]

    // Filter updates to only include allowed fields
    const filteredUpdates: Record<string, unknown> = {}
    for (const [key, value] of Object.entries(updates)) {
      if (allowedFields.includes(key)) {
        filteredUpdates[key] = value
      }
    }

    // Add updated_at timestamp
    filteredUpdates.updated_at = new Date().toISOString()

    console.log('Filtered updates:', filteredUpdates)

    // Check if settings record exists
    const { data: existingSettings, error: selectError } = await supabase
      .from('site_settings')
      .select('id')
      .maybeSingle()

    // Log for debugging
    console.log('Existing settings check:', { existingSettings, selectError })

    let result
    if (existingSettings) {
      // Update existing record
      console.log('Updating existing settings with ID:', existingSettings.id)
      result = await supabase
        .from('site_settings')
        .update(filteredUpdates)
        .eq('id', existingSettings.id)
        .select()
        .single()
    } else {
      // Create new record
      console.log('Creating new settings record')
      result = await supabase
        .from('site_settings')
        .insert({
          id: crypto.randomUUID(),
          ...filteredUpdates
        })
        .select()
        .single()
    }

    if (result.error) {
      console.error('Error saving settings:', result.error)
      return NextResponse.json(
        { error: 'Error saving settings' },
        { status: 500 }
      )
    }

    return NextResponse.json(result.data)
  } catch (error) {
    console.error('Error in POST /api/admin/settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
